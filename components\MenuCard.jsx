/* eslint-disable react/prop-types */
"use client";
import React, { useState, useMemo } from "react";
import {
  softgetraenke,
  shishas,
  saefte,
  heissgetraenke,
  bier,
  cocktails,
  mocktailsOhneAlkohol,
  aperitif,
  likoere,
  spritz,
  vodka,
  whiskys,
  rum,
  gin,
  wein,
  prosecco,
  champagner,
  shots,
  speisen,
  snacks,
} from "./menu";
import {
  Box,
  Container,
  Divider,
  Typography,
  TextField,
  InputAdornment,
  Chip,
  Card,
  CardContent,
  Fade,
  IconButton,
  useTheme,
  alpha,
} from "@mui/material";
import Image from "next/image";
import { BiSearch } from "react-icons/bi";
import { MdClear, MdLocalOffer } from "react-icons/md";

// MenuItem Component with accessibility features
const MenuItem = ({ item, hasPromotion = false, ...props }) => {
  const theme = useTheme();
  const { code, name, price, newPrice } = item;

  return (
    <Box
      component="article"
      role="listitem"
      aria-label={`${name}, Code: ${code}, Preis: ${newPrice || price} CHF${
        hasPromotion ? ", Sonderangebot" : ""
      }`}
      sx={{
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center",
        gap: 2,
        py: { xs: 1.5, md: 2 },
        px: { xs: 1, md: 2 },
        borderRadius: 2,
        transition: "all 0.3s ease",
        "&:hover": {
          backgroundColor: alpha(theme.palette.primary.main, 0.05),
          transform: "translateX(4px)",
        },
        "&:focus-within": {
          backgroundColor: alpha(theme.palette.primary.main, 0.08),
          outline: `2px solid ${theme.palette.primary.main}`,
          outlineOffset: "2px",
        },
      }}
      {...props}
    >
      <Box sx={{ display: "flex", alignItems: "center", gap: 1, flex: 1 }}>
        {hasPromotion && (
          <MdLocalOffer
            sx={{
              color: "#67f756",
              fontSize: 16,
              animation: "pulse 2s infinite",
            }}
            aria-hidden="true"
          />
        )}
        <Box>
          <Typography
            variant="body1"
            component="h4"
            sx={{
              fontSize: { xs: 14, sm: 16, md: 18 },
              fontWeight: hasPromotion ? 600 : 500,
              color: "text.primary",
              lineHeight: 1.3,
            }}
          >
            {name}
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: { xs: 12, sm: 14 },
              color: "#b4d1b0",
              fontFamily: "monospace",
              opacity: 0.8,
            }}
            aria-label={`Produktcode: ${code}`}
          >
            {code}
          </Typography>
        </Box>
      </Box>

      <Box
        sx={{
          flexGrow: 1,
          mx: 2,
          display: { xs: "none", sm: "block" },
        }}
      >
        <Divider
          sx={{
            borderColor: alpha("#67f756", 0.3),
            borderStyle: "dotted",
          }}
        />
      </Box>

      <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
        {newPrice !== null && newPrice !== undefined ? (
          <>
            <Typography
              variant="body2"
              sx={{
                fontSize: { xs: 12, sm: 14 },
                textDecoration: "line-through",
                color: "error.main",
                opacity: 0.7,
              }}
              aria-label={`Alter Preis: ${price.toFixed(2)} CHF`}
            >
              {price.toFixed(2)}
            </Typography>
            <Typography
              variant="body1"
              sx={{
                fontSize: { xs: 14, sm: 16, md: 18 },
                fontWeight: 600,
                color: "#67f756",
              }}
              aria-label={`Neuer Preis: ${newPrice.toFixed(2)} CHF`}
            >
              {newPrice.toFixed(2)} CHF
            </Typography>
          </>
        ) : (
          <Typography
            variant="body1"
            sx={{
              fontSize: { xs: 14, sm: 16, md: 18 },
              fontWeight: 500,
              color: "text.primary",
            }}
            aria-label={`Preis: ${price.toFixed(2)} CHF`}
          >
            {price.toFixed(2)} CHF
          </Typography>
        )}
      </Box>
    </Box>
  );
};

// MenuSection Component with accessibility features
const MenuSection = ({
  section,
  items,
  imageUrl,
  hasPromotion = false,
  searchTerm = "",
}) => {
  // Filter items based on search term
  const filteredItems = useMemo(() => {
    if (!searchTerm) return items;

    const term = searchTerm.toLowerCase();
    return items.filter(
      (item) =>
        item.name.toLowerCase().includes(term) ||
        item.code.toLowerCase().includes(term)
    );
  }, [items, searchTerm]);

  if (filteredItems.length === 0 && searchTerm) {
    return null; // Don't render section if no items match search
  }

  return (
    <Card
      component="section"
      aria-labelledby={`section-${section.replace(/\s+/g, "-").toLowerCase()}`}
      sx={{
        background: imageUrl
          ? `linear-gradient(rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.7)), url(${imageUrl})`
          : "rgba(255, 255, 255, 0.02)",
        backgroundSize: "cover",
        backgroundPosition: "center",
        border: `2px solid ${alpha("#67f756", 0.6)}`,
        borderRadius: 3,
        overflow: "hidden",
        mb: 4,
        transition: "all 0.3s ease",
        "&:hover": {
          borderColor: "#67f756",
          boxShadow: `0 8px 32px ${alpha("#67f756", 0.2)}`,
        },
      }}
    >
      <CardContent sx={{ p: { xs: 2, md: 3 } }}>
        {/* Section Header */}
        <Box sx={{ mb: 3, textAlign: "center" }}>
          {hasPromotion && (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                gap: 2,
                mb: 2,
                flexWrap: "wrap",
              }}
            >
              <Image
                src="/assets/offer.png"
                alt="Winteraktion"
                width={40}
                height={40}
                style={{ filter: "drop-shadow(0 0 8px #67f756)" }}
              />
              <Typography
                variant="h3"
                sx={{
                  fontSize: { xs: 18, sm: 22, md: 25 },
                  color: "#67f756",
                  fontWeight: 600,
                  textShadow: "0 0 10px rgba(103, 247, 86, 0.5)",
                }}
              >
                Sommeraktion
              </Typography>
            </Box>
          )}

          <Typography
            id={`section-${section.replace(/\s+/g, "-").toLowerCase()}`}
            variant="h2"
            component="h3"
            sx={{
              fontSize: { xs: 22, sm: 26, md: 30 },
              fontWeight: 700,
              color: "text.primary",
              mb: 1,
              textTransform: "uppercase",
              letterSpacing: "0.05em",
            }}
          >
            {section}
          </Typography>

          {searchTerm && (
            <Typography
              variant="body2"
              sx={{
                color: "text.secondary",
                fontSize: { xs: 12, sm: 14 },
              }}
            >
              {filteredItems.length} von {items.length} Artikeln
            </Typography>
          )}
        </Box>

        {/* Items List */}
        <Box
          component="ul"
          role="list"
          aria-label={`${section} Menüpunkte`}
          sx={{
            listStyle: "none",
            p: 0,
            m: 0,
            "& > *:not(:last-child)": {
              borderBottom: `1px solid ${alpha("#67f756", 0.1)}`,
            },
          }}
        >
          {filteredItems.map((item, index) => (
            <MenuItem
              key={`${item.code}-${index}`}
              item={item}
              hasPromotion={
                hasPromotion &&
                item.newPrice !== null &&
                item.newPrice !== undefined
              }
              tabIndex={0}
            />
          ))}
        </Box>
      </CardContent>
    </Card>
  );
};

function MenuCard() {
  const [searchTerm, setSearchTerm] = useState("");

  // All menu sections data
  const menuSections = useMemo(
    () => [
      {
        ...shishas,
        imageUrl:
          "https://images.ctfassets.net/87jhdyn6f199/66R8uiPd4OQdR9fwYdFKBi/9e616a6997c7e728f3aeccea83f4cef2/IMG_9793.JPG",
        hasPromotion: true,
      },
      { ...softgetraenke, hasPromotion: false },
      { ...saefte, hasPromotion: false },
      { ...heissgetraenke, hasPromotion: false },
      { ...bier, hasPromotion: false },
      { ...cocktails, hasPromotion: false },
      { ...mocktailsOhneAlkohol, hasPromotion: false },
      { ...aperitif, hasPromotion: false },
      { ...likoere, hasPromotion: false },
      { ...spritz, hasPromotion: false },
      { ...vodka, hasPromotion: false },
      { ...whiskys, hasPromotion: false },
      { ...rum, hasPromotion: false },
      { ...gin, hasPromotion: false },
      { ...wein, hasPromotion: false },
      { ...prosecco, hasPromotion: false },
      { ...champagner, hasPromotion: false },
      { ...shots, hasPromotion: false },
      { ...speisen, hasPromotion: false },
      { ...snacks, hasPromotion: false },
    ],
    []
  );

  // Filter sections that have matching items
  const filteredSections = useMemo(() => {
    if (!searchTerm) return menuSections;

    const term = searchTerm.toLowerCase();
    return menuSections.filter(
      (section) =>
        section.items.some(
          (item) =>
            item.name.toLowerCase().includes(term) ||
            item.code.toLowerCase().includes(term)
        ) || section.section.toLowerCase().includes(term)
    );
  }, [menuSections, searchTerm]);

  const handleSearchChange = (event) => {
    setSearchTerm(event.target.value);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  return (
    <Box
      sx={{
        background: `
          radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.03) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.05) 0%, transparent 50%),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
        minHeight: "100vh",
        py: { xs: 4, md: 6 },
      }}
    >
      <Container maxWidth="lg">
        {/* Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 4, md: 6 } }}>
          <Typography
            variant="h1"
            component="h2"
            sx={{
              fontSize: { xs: 28, sm: 36, md: 44 },
              fontWeight: 800,
              color: "text.primary",
              mb: 2,
              textTransform: "uppercase",
              letterSpacing: "0.1em",
              background: "linear-gradient(135deg, #67f756 0%, #4ade80 100%)",
              backgroundClip: "text",
              WebkitBackgroundClip: "text",
              WebkitTextFillColor: "transparent",
              textShadow: "0 0 30px rgba(103, 247, 86, 0.3)",
            }}
          >
            Menükarte
          </Typography>

          <Typography
            variant="h6"
            sx={{
              color: "text.secondary",
              fontSize: { xs: 14, sm: 16, md: 18 },
              maxWidth: 600,
              mx: "auto",
              mb: 4,
            }}
          >
            Entdecken Sie unsere exquisite Auswahl an Getränken, Shishas und
            Speisen
          </Typography>

          {/* Search Bar */}
          <Box
            sx={{
              maxWidth: 500,
              mx: "auto",
              position: "relative",
            }}
          >
            <TextField
              fullWidth
              variant="outlined"
              placeholder="Suchen Sie nach Getränken, Speisen oder Codes..."
              value={searchTerm}
              onChange={handleSearchChange}
              aria-label="Menü durchsuchen"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <BiSearch sx={{ color: "#67f756" }} />
                  </InputAdornment>
                ),
                endAdornment: searchTerm && (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={clearSearch}
                      edge="end"
                      aria-label="Suche löschen"
                      size="small"
                    >
                      <MdClear />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: alpha("#000", 0.3),
                  borderRadius: 3,
                  "& fieldset": {
                    borderColor: alpha("#67f756", 0.3),
                    borderWidth: 2,
                  },
                  "&:hover fieldset": {
                    borderColor: alpha("#67f756", 0.6),
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "#67f756",
                    boxShadow: `0 0 20px ${alpha("#67f756", 0.3)}`,
                  },
                },
                "& .MuiInputBase-input": {
                  color: "text.primary",
                  fontSize: { xs: 14, sm: 16 },
                  "&::placeholder": {
                    color: "text.secondary",
                    opacity: 0.7,
                  },
                },
              }}
            />
          </Box>

          {/* Search Results Summary */}
          {searchTerm && (
            <Fade in={Boolean(searchTerm)}>
              <Box sx={{ mt: 2 }}>
                <Chip
                  label={`${filteredSections.length} Kategorien gefunden`}
                  sx={{
                    backgroundColor: alpha("#67f756", 0.2),
                    color: "#67f756",
                    fontWeight: 600,
                  }}
                />
              </Box>
            </Fade>
          )}
        </Box>

        {/* Menu Sections */}
        <Box>
          {filteredSections.length > 0 ? (
            filteredSections.map((section, index) => (
              <Fade key={section.section} in={true} timeout={300 + index * 100}>
                <div>
                  <MenuSection
                    section={section.section}
                    items={section.items}
                    imageUrl={section.imageUrl}
                    hasPromotion={section.hasPromotion}
                    searchTerm={searchTerm}
                  />
                </div>
              </Fade>
            ))
          ) : (
            <Fade in={true}>
              <Card
                sx={{
                  textAlign: "center",
                  p: 4,
                  backgroundColor: alpha("#000", 0.3),
                  border: `2px solid ${alpha("#67f756", 0.3)}`,
                  borderRadius: 3,
                }}
              >
                <Typography
                  variant="h6"
                  sx={{ color: "text.secondary", mb: 2 }}
                >
                  Keine Ergebnisse gefunden
                </Typography>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  Versuchen Sie es mit anderen Suchbegriffen oder{" "}
                  <Box
                    component="button"
                    onClick={clearSearch}
                    sx={{
                      background: "none",
                      border: "none",
                      color: "#67f756",
                      cursor: "pointer",
                      textDecoration: "underline",
                      fontSize: "inherit",
                      fontFamily: "inherit",
                    }}
                  >
                    löschen Sie die Suche
                  </Box>
                </Typography>
              </Card>
            </Fade>
          )}
        </Box>
      </Container>
    </Box>
  );
}

export default MenuCard;
