import { Box, Button, Container, Grid, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import pxToRem from "@/ThemeRegistry/pxToRem";

function GridSection() {
  return (
    <>
      {/* First Section - About Us */}
      <Box
        sx={{
          position: "relative",
          background: `
            radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.05) 0%, transparent 50%),
            radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.08) 0%, transparent 50%),
            linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
          `,
          py: { xs: 8, md: 12 },
          overflow: "hidden",
        }}
      >
        {/* Floating background elements */}
        <Box
          sx={{
            position: "absolute",
            top: "10%",
            left: "5%",
            width: "150px",
            height: "150px",
            background:
              "radial-gradient(circle, rgba(103, 247, 86, 0.06) 0%, transparent 70%)",
            borderRadius: "50%",
            filter: "blur(30px)",
            animation: "float 8s ease-in-out infinite",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            bottom: "20%",
            right: "10%",
            width: "200px",
            height: "200px",
            background:
              "radial-gradient(circle, rgba(74, 29, 31, 0.04) 0%, transparent 70%)",
            borderRadius: "50%",
            filter: "blur(40px)",
            animation: "float 10s ease-in-out infinite reverse",
          }}
        />

        <Container sx={{ position: "relative", zIndex: 2 }}>
          <Grid container spacing={{ xs: 4, md: 8 }} alignItems="center">
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  position: "relative",
                  borderRadius: "24px",
                  overflow: "hidden",
                  background: "rgba(255, 255, 255, 0.02)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  transition: "all 0.4s ease-in-out",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: "0 20px 40px rgba(103, 247, 86, 0.1)",
                    border: "1px solid rgba(103, 247, 86, 0.2)",
                  },
                }}
              >
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/3l4ylLkF1qrWJUz4ldSh8P/91136f6cfe0b8f21d35e13fe2c50af0a/photo_2023-11-05_17-32-09.jpg"
                  alt="Vibes Rooftop Exklusivität"
                  height={0}
                  width={0}
                  sizes="100vw"
                  className="image"
                  style={{ borderRadius: "24px" }}
                />
                {/* Modern overlay effect */}
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    background: `
                        linear-gradient(135deg,
                          rgba(103,247,86,0.05) 0%,
                          transparent 30%,
                          transparent 70%,
                          rgba(74,29,31,0.05) 100%
                        )
                      `,
                    borderRadius: "24px",
                    pointerEvents: "none",
                  }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ pl: { md: 4 } }}>
                {/* Modern badge */}
                <Box
                  sx={{
                    display: "inline-flex",
                    alignItems: "center",
                    backgroundColor: "rgba(103, 247, 86, 0.1)",
                    border: "1px solid rgba(103, 247, 86, 0.3)",
                    borderRadius: "50px",
                    px: 3,
                    py: 1,
                    mb: 3,
                    backdropFilter: "blur(10px)",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#67f756",
                      fontWeight: "600",
                      fontSize: pxToRem(14),
                      letterSpacing: "0.5px",
                      textTransform: "uppercase",
                    }}
                  >
                    Über uns
                  </Typography>
                </Box>

                <Typography
                  variant="h2"
                  sx={{
                    fontSize: `clamp(${pxToRem(28)}, 5vw, ${pxToRem(42)})`,
                    fontWeight: 700,
                    mb: 3,
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    lineHeight: 1.2,
                  }}
                >
                  Genieße die Exklusivität von Vibes Rooftop
                </Typography>

                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    display: "block",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                    mb: 4,
                  }}
                >
                  Willkommen bei Vibes Rooftop – dem exklusiven Shisha Bar und
                  Eventort, der deine Sinne verzaubert und dir unvergessliche
                  Momente in einer luxuriösen Umgebung bietet. Unsere
                  atemberaubende Dachterrasse inmitten der malerischen Schweizer
                  Landschaft lädt dich ein, den Alltag zu vergessen und dich in
                  eine Welt der Raffinesse und Entspannung zu entführen.
                </Typography>

                <Link href="/kontakt" style={{ textDecoration: "none" }}>
                  <Button variant="contained">Buche jetzt!</Button>
                </Link>
              </Box>
            </Grid>
          </Grid>
        </Container>

        <Container sx={{ position: "relative", zIndex: 2, mt: 10 }}>
          <Grid
            container
            spacing={{ xs: 4, md: 8 }}
            alignItems="center"
            direction={{ xs: "column", md: "row-reverse" }}
          >
            <Grid item xs={12} md={6}>
              <Box
                sx={{
                  position: "relative",
                  borderRadius: "24px",
                  overflow: "hidden",
                  background: "rgba(255, 255, 255, 0.02)",
                  backdropFilter: "blur(10px)",
                  border: "1px solid rgba(255, 255, 255, 0.1)",
                  transition: "all 0.4s ease-in-out",
                  "&:hover": {
                    transform: "translateY(-8px)",
                    boxShadow: "0 20px 40px rgba(103, 247, 86, 0.1)",
                    border: "1px solid rgba(103, 247, 86, 0.2)",
                  },
                }}
              >
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/3Pu29EwF9cGOKrmizyFPmC/9a7d3e30ed70233d00fd36ff686b0504/photo_2023-11-06_12-24-14.jpg"
                  alt="Vibes Rooftop Lounge"
                  height={0}
                  width={0}
                  sizes="100vw"
                  className="image"
                  style={{ borderRadius: "24px" }}
                />
                {/* Modern overlay effect */}
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    width: "100%",
                    height: "100%",
                    background: `
                        linear-gradient(135deg,
                          rgba(74,29,31,0.05) 0%,
                          transparent 30%,
                          transparent 70%,
                          rgba(103,247,86,0.05) 100%
                        )
                      `,
                    borderRadius: "24px",
                    pointerEvents: "none",
                  }}
                />
              </Box>
            </Grid>
            <Grid item xs={12} md={6}>
              <Box sx={{ pr: { md: 4 } }}>
                {/* Modern badge */}
                <Box
                  sx={{
                    display: "inline-flex",
                    alignItems: "center",
                    backgroundColor: "rgba(103, 247, 86, 0.1)",
                    border: "1px solid rgba(103, 247, 86, 0.3)",
                    borderRadius: "50px",
                    px: 3,
                    py: 1,
                    mb: 3,
                    backdropFilter: "blur(10px)",
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: "#67f756",
                      fontWeight: "600",
                      fontSize: pxToRem(14),
                      letterSpacing: "0.5px",
                      textTransform: "uppercase",
                    }}
                  >
                    Unsere Lounge
                  </Typography>
                </Box>

                <Typography
                  variant="h2"
                  sx={{
                    fontSize: `clamp(${pxToRem(28)}, 5vw, ${pxToRem(42)})`,
                    fontWeight: 700,
                    mb: 3,
                    background:
                      "linear-gradient(135deg, #ffffff 0%, #e0e0e0 100%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                    lineHeight: 1.2,
                  }}
                >
                  Verbringen Sie die perfekte Zeit mit Freunden
                </Typography>

                <Typography
                  variant="body1"
                  sx={{
                    color: "text.secondary",
                    display: "block",
                    fontSize: pxToRem(16),
                    lineHeight: 1.7,
                    mb: 4,
                  }}
                >
                  In unserer Lounge erlebst du die vollendete Symbiose aus Stil,
                  Komfort und Geselligkeit. Hier kannst du die perfekte Zeit mit
                  Freunden verbringen, während du von der atemberaubenden
                  Aussicht auf die umliegende Schweizer Landschaft fasziniert
                  wirst. Unsere gemütlichen Sitzgelegenheiten und das elegante
                  Ambiente schaffen den idealen Rahmen für anregende Gespräche,
                  gemeinsames Lachen und unvergessliche Erlebnisse.
                </Typography>

                <Link href="/gallerie" style={{ textDecoration: "none" }}>
                  <Button variant="outlined">Galerie ansehen</Button>
                </Link>
              </Box>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </>
  );
}

export default GridSection;
