import { Box, Button, Container, Grid, Typography } from "@mui/material";
import Image from "next/image";
import Link from "next/link";
import React from "react";

function GridSection() {
  return (
    <>
      <Box
        sx={{
          position: "relative",
          height: "100%",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "100%",
            width: "100%",
            background:
              "url(https://images.ctfassets.net/87jhdyn6f199/1AuR55SkhlsH1dbuAVm7It/0e01d47b2cbc9ef3b134f61244d94f9b/iefubvef.png)",
            backgroundPosition: "top left",
            backgroundSize: "285px auto",
            backgroundRepeat: "no-repeat",
          }}
        />
        <Container sx={{ my: 8, position: { xs: "unset", md: "relative" } }}>
          <Grid container spacing={6} alignItems={"center"}>
            <Grid item xs={12} md={6}>
              <Image
                src="https://images.ctfassets.net/87jhdyn6f199/3l4ylLkF1qrWJUz4ldSh8P/91136f6cfe0b8f21d35e13fe2c50af0a/photo_2023-11-05_17-32-09.jpg"
                alt="test"
                height={0}
                width={0}
                sizes="100vw"
                className="image"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography
                variant="body1"
                gutterBottom
                display={"block"}
                color={"primary"}
                fontSize={22}
              >
                Über uns
              </Typography>
              <Typography
                variant="h1"
                gutterBottom
                display={"block"}
                fontSize={{ xs: 25, md: 35 }}
              >
                Genieße die Exklusivität von Vibes Rooftop
              </Typography>
              <Typography
                variant="body1"
                gutterBottom
                display={"block"}
                color={"text.secondary"}
              >
                Willkommen bei Vibes Rooftop – dem exklusiven Shisha Bar und
                Eventort, der deine Sinne verzaubert und dir unvergessliche
                Momente in einer luxuriösen Umgebung bietet. Unsere
                atemberaubende Dachterrasse inmitten der malerischen Schweizer
                Landschaft lädt dich ein, den Alltag zu vergessen und dich in
                eine Welt der Raffinesse und Entspannung zu entführen.
              </Typography>
              <Link href="/kontakt">
                <Button variant="outlined" color="primary" sx={{ mt: 2 }}>
                  Buche jetzt!
                </Button>
              </Link>
            </Grid>
          </Grid>
        </Container>
      </Box>
      <Box
        sx={{
          position: "relative",
          height: "100%",
        }}
      >
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            height: "100%",
            width: "100%",
            background:
              "url(https://images.ctfassets.net/87jhdyn6f199/24H1Nuws4G6SgfJSS3b11h/892bdf83f3b546d4ed780bf27f7c7aff/o4itgj4t.png)",
            backgroundPosition: "bottom right",
            backgroundSize: "285px auto",
            backgroundRepeat: "no-repeat",
          }}
        />
        <Container sx={{ my: 8, position: { xs: "unset", md: "relative" } }}>
          <Grid
            container
            spacing={6}
            alignItems={"center"}
            flexDirection={{ xs: "column", md: "row-reverse" }}
          >
            <Grid item xs={12} md={6}>
              <Image
                src="https://images.ctfassets.net/87jhdyn6f199/3Pu29EwF9cGOKrmizyFPmC/9a7d3e30ed70233d00fd36ff686b0504/photo_2023-11-06_12-24-14.jpg"
                alt="test"
                height={0}
                width={0}
                sizes="100vw"
                className="image"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography
                variant="body1"
                gutterBottom
                display={"block"}
                color={"primary"}
                fontSize={22}
              >
                Unsere Lounge
              </Typography>
              <Typography
                variant="h1"
                gutterBottom
                display={"block"}
                fontSize={{ xs: 25, md: 35 }}
              >
                Verbringen Sie die perfekte Zeit mit Freunden
              </Typography>
              <Typography
                variant="body1"
                gutterBottom
                display={"block"}
                color={"text.secondary"}
              >
                In unserer Lounge erlebst du die vollendete Symbiose aus Stil,
                Komfort und Geselligkeit. Hier kannst du die perfekte Zeit mit
                Freunden verbringen, während du von der atemberaubenden Aussicht
                auf die umliegende Schweizer Landschaft fasziniert wirst. Unsere
                gemütlichen Sitzgelegenheiten und das elegante Ambiente schaffen
                den idealen Rahmen für anregende Gespräche, gemeinsames Lachen
                und unvergessliche Erlebnisse.
              </Typography>
              <Link href={"/gallerie"}>
                <Button variant="outlined" color="primary" sx={{ mt: 2 }}>
                  Galerie ansehen
                </Button>
              </Link>
            </Grid>
          </Grid>
        </Container>
      </Box>
    </>
  );
}

export default GridSection;
