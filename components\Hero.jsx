import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Container, Box } from "@mui/material";
import pxToRem from "@/ThemeRegistry/pxToRem";
import Link from "next/link";

const HeroSection = {
  position: "relative",
  // top: { xs: -30, sm: -60 },
  height: { xs: "75vh", md: "85vh" },
  width: "100%",
  overflow: "hidden",
};

const VideoBackground = {
  width: "100%",
  height: "100%",
  objectFit: "cover",
};

const Overlay = {
  position: "absolute",
  top: 0,
  left: 0,
  width: "100%",
  height: "100%",
  background: `
    radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.15) 0%, transparent 50%),
    linear-gradient(135deg, rgba(9, 9, 11, 0.7) 0%, rgba(15, 15, 15, 0.8) 50%, rgba(9, 9, 11, 0.7) 100%)
  `,
};

const HeroSe = () => {
  return (
    <Box sx={HeroSection}>
      <video autoPlay muted loop playsInline style={VideoBackground}>
        <source
          src="https://videos.ctfassets.net/87jhdyn6f199/610Hv3xbGof0SMwq7OExZs/00bef607dc15ec1b3cf6dec3a7ccdd7b/vid.mp4"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>
      <div style={Overlay} />

      {/* Modern geometric background pattern */}
      <Box
        sx={{
          position: "absolute",
          top: 0,
          left: 0,
          width: "100%",
          height: "100%",
          background: `
            linear-gradient(45deg, transparent 30%, rgba(103, 247, 86, 0.02) 30%, rgba(103, 247, 86, 0.02) 32%, transparent 32%),
            linear-gradient(-45deg, transparent 30%, rgba(103, 247, 86, 0.02) 30%, rgba(103, 247, 86, 0.02) 32%, transparent 32%)
          `,
          backgroundSize: "60px 60px",
          zIndex: 1,
        }}
      />

      {/* Floating orbs */}
      <Box
        sx={{
          position: "absolute",
          top: "20%",
          left: "10%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.08) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(40px)",
          animation: "float 6s ease-in-out infinite",
        }}
      />
      <Box
        sx={{
          position: "absolute",
          bottom: "20%",
          right: "15%",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.1) 0%, transparent 70%)",
          borderRadius: "50%",
          filter: "blur(30px)",
          animation: "float 8s ease-in-out infinite reverse",
        }}
      />

      <Container
        sx={{
          position: "absolute",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          textAlign: { xs: "center", sm: "left" },
          color: "#ffffff",
          zIndex: 2,
        }}
      >
        <Box sx={{ maxWidth: "800px" }}>
          {/* Modern badge */}
          <Box
            sx={{
              display: "inline-flex",
              alignItems: "center",
              gap: 1,
              backgroundColor: "rgba(103, 247, 86, 0.1)",
              border: "1px solid rgba(103, 247, 86, 0.3)",
              borderRadius: "50px",
              px: 3,
              py: 1,
              mb: 3,
              backdropFilter: "blur(10px)",
              animation: "fadeInUp 1s ease-out",
            }}
          >
            <Typography
              variant="body2"
              sx={{
                color: "#67f756",
                fontWeight: "600",
                fontSize: { xs: "0.9rem", md: "1rem" },
                letterSpacing: "0.5px",
              }}
            >
              Vibes Rooftop
            </Typography>
          </Box>

          <Typography
            variant="h1"
            sx={{
              fontSize: `clamp(${pxToRem(32)}, 7vw, ${pxToRem(65)})`,
              mb: 2,
              animation: "slideInFromLeft 1s ease-out 0.3s both",
            }}
          >
            ÜBER DEN DÄCHERN VON BASEL
          </Typography>
          <Typography
            variant="h4"
            color="#C6C6C6"
            component="h4"
            sx={{
              fontSize: { xs: 16, md: 19 },
              mb: 4,
              animation: "slideInFromLeft 1s ease-out 0.6s both",
            }}
          >
            Erleben Sie exklusive Shisha und Cocktails in unserem Lounge-Bar in
            Basel
          </Typography>

          <Box
            sx={{
              display: "flex",
              flexDirection: { xs: "column", sm: "row" },
              gap: 2,
              alignItems: { xs: "center", sm: "flex-start" },
              animation: "fadeInUp 1s ease-out 0.9s both",
            }}
          >
            <a href="#form">
              <Button
                variant="contained"
                size="large"
                sx={{
                  px: { xs: 4, md: 6 },
                  py: { xs: 2, md: 2.5 },
                  fontSize: { xs: "1rem", md: "1.1rem" },
                  minWidth: { xs: "280px", sm: "auto" },
                }}
              >
                Jetzt reservieren!
              </Button>
            </a>
            <Link href="/uber-uns">
              <Button
                variant="outlined"
                size="large"
                sx={{
                  px: { xs: 4, md: 6 },
                  py: { xs: 2, md: 2.5 },
                  fontSize: { xs: "1rem", md: "1.1rem" },
                  minWidth: { xs: "280px", sm: "auto" },
                }}
              >
                Mehr erfahren
              </Button>
            </Link>
          </Box>
        </Box>
      </Container>
    </Box>
  );
};

const HeroComponent = () => {
  return (
    <>
      <HeroSe />
    </>
  );
};

export default HeroComponent;
