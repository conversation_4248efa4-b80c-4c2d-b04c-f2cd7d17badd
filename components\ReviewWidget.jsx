"use client";

import React, { useEffect, useRef } from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  Fade,
  Rating,
  Button,
} from "@mui/material";
import { BsGoogle } from "react-icons/bs";

const ReviewWidget = () => {
  const widgetRef = useRef(null);

  // Load Shapo widget
  useEffect(() => {
    // Load Shapo embed script
    const script = document.createElement("script");
    script.id = "shapo-embed-js";
    script.type = "text/javascript";
    script.src = "https://cdn.shapo.io/js/embed.js";
    script.defer = true;

    // Check if script is already loaded
    if (!document.getElementById("shapo-embed-js")) {
      document.head.appendChild(script);
    }

    // Custom CSS for better integration with your theme
    const style = document.createElement("style");
    style.textContent = `
      #shapo-widget-3e0bf9a1e43692528293 {
        border-radius: 20px !important;
        min-height: 500px !important;
      }


      .shapo-review-card {
        border: 1px solid rgba(103, 247, 86, 0.1) !important;
        border-radius: 16px !important;
        backdrop-filter: blur(10px) !important;
      }

      .shapo-review-text {
        color: #C6C6C6 !important;
      }

      .shapo-reviewer-name {
        color: #ffffff !important;
      }

      .shapo-star-rating svg {
        color: #67f756 !important;
      }

      .shapo-widget iframe {
        border-radius: 20px !important;
        border: none !important;
      }
    `;

    if (!document.getElementById("shapo-custom-styles")) {
      style.id = "shapo-custom-styles";
      document.head.appendChild(style);
    }

    return () => {
      // Cleanup on unmount
      const existingScript = document.getElementById("shapo-embed-js");
      const existingStyle = document.getElementById("shapo-custom-styles");

      if (existingScript && document.head.contains(existingScript)) {
        document.head.removeChild(existingScript);
      }
      if (existingStyle && document.head.contains(existingStyle)) {
        document.head.removeChild(existingStyle);
      }
    };
  }, []);

  const averageRating = 5.0; // You can update this with real data from Shapo
  const totalReviews = "50+"; // You can update this with real data from Shapo

  return (
    <Box
      component="section"
      sx={{
        py: { xs: 8, md: 12 },
        position: "relative",
        overflow: "hidden",
        background: `
          radial-gradient(circle at 30% 70%, rgba(103, 247, 86, 0.08) 0%, transparent 60%),
          radial-gradient(circle at 70% 30%, rgba(74, 29, 31, 0.12) 0%, transparent 60%),
          linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, transparent 50%)
        `,
      }}
    >
      {/* Animated background elements */}
      <Box
        sx={{
          position: "absolute",
          top: "10%",
          left: "5%",
          width: "200px",
          height: "200px",
          background:
            "radial-gradient(circle, rgba(103, 247, 86, 0.06) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "float 6s ease-in-out infinite",
          "@keyframes float": {
            "0%, 100%": { transform: "translateY(0px) rotate(0deg)" },
            "50%": { transform: "translateY(-20px) rotate(180deg)" },
          },
        }}
      />
      <Box
        sx={{
          position: "absolute",
          top: "60%",
          right: "10%",
          width: "150px",
          height: "150px",
          background:
            "radial-gradient(circle, rgba(74, 29, 31, 0.08) 0%, transparent 70%)",
          borderRadius: "50%",
          animation: "float 8s ease-in-out infinite reverse",
        }}
      />

      <Container sx={{ position: "relative", zIndex: 1 }}>
        {/* Section Header */}
        <Box sx={{ textAlign: "center", mb: { xs: 6, md: 8 } }}>
          <Fade in timeout={800}>
            <Box>
              {/* Rating Overview */}
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  gap: 2,
                  mb: 4,
                  flexWrap: "wrap",
                }}
              >
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <BsGoogle style={{ color: "#4285f4", fontSize: "24px" }} />
                  <Typography variant="h6" sx={{ fontWeight: 600 }}>
                    Google Reviews
                  </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Rating
                    value={averageRating}
                    readOnly
                    precision={0.1}
                    sx={{
                      "& .MuiRating-iconFilled": {
                        color: "#67f756",
                      },
                    }}
                  />
                  <Typography
                    variant="h6"
                    sx={{ fontWeight: 700, color: "primary.main" }}
                  >
                    {averageRating.toFixed(1)}
                  </Typography>
                  <Typography variant="body2" sx={{ color: "text.secondary" }}>
                    ({totalReviews} Bewertungen)
                  </Typography>
                </Box>
              </Box>

              <Typography
                variant="h1"
                component="h2"
                sx={{
                  fontSize: { xs: 32, md: 48 },
                  fontWeight: 800,
                  mb: 3,
                  background:
                    "linear-gradient(135deg, #ffffff 0%, #67f756 100%)",
                  backgroundClip: "text",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  textAlign: "center",
                  letterSpacing: "-0.02em",
                }}
              >
                Was unsere Gäste sagen
              </Typography>

              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  fontSize: { xs: 18, md: 20 },
                  maxWidth: "700px",
                  mx: "auto",
                  lineHeight: 1.6,
                  fontWeight: 400,
                }}
              >
                Erleben Sie, warum Vibes Rooftop der perfekte Ort für
                unvergessliche Momente ist
              </Typography>
            </Box>
          </Fade>
        </Box>

        {/* Featured Review Carousel */}
        <Fade in timeout={1200}>
          <Box sx={{ mb: { xs: 6, md: 8 } }}>
            <Paper
              elevation={0}
              sx={{
                p: { xs: 4, md: 6 },
                background: `
                  linear-gradient(135deg,
                    rgba(255, 255, 255, 0.12) 0%,
                    rgba(255, 255, 255, 0.08) 50%,
                    rgba(103, 247, 86, 0.1) 100%
                  )
                `,
                backdropFilter: "blur(20px)",
                border: "2px solid rgba(103, 247, 86, 0.2)",
                borderRadius: "32px",
                position: "relative",
                overflow: "hidden",
                transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                boxShadow: "0 8px 32px rgba(103, 247, 86, 0.1)",
                "&:hover": {
                  transform: "translateY(-6px)",
                  boxShadow: "0 24px 48px rgba(103, 247, 86, 0.2)",
                  border: "2px solid rgba(103, 247, 86, 0.4)",
                },
              }}
            >
              {/* Widget Header */}
              <Box
                sx={{
                  textAlign: "center",
                  mb: 4,
                }}
              >
                <Typography
                  variant="h5"
                  sx={{ fontWeight: 700, color: "primary.main", mb: 1 }}
                >
                  Echte Google Bewertungen
                </Typography>
                <Typography variant="body2" sx={{ color: "text.secondary" }}>
                  Direkt von unseren zufriedenen Gästen
                </Typography>
              </Box>

              {/* Shapo Widget Container */}
              <Box
                ref={widgetRef}
                sx={{
                  position: "relative",
                  minHeight: { xs: "400px", md: "500px" },
                  display: "flex",
                  justifyContent: "center",
                  alignItems: "center",
                  "& #shapo-widget-3e0bf9a1e43692528293": {
                    width: "100%",
                    minHeight: { xs: "400px", md: "500px" },
                    borderRadius: "20px",
                  },
                }}
              >
                <div id="shapo-widget-3e0bf9a1e43692528293"></div>
              </Box>
            </Paper>
          </Box>
        </Fade>

        {/* Call to Action */}
        <Fade in timeout={2000}>
          <Box sx={{ textAlign: "center" }}>
            <Paper
              elevation={0}
              sx={{
                p: { xs: 4, md: 6 },
                background: `
                  linear-gradient(135deg,
                    rgba(103, 247, 86, 0.1) 0%,
                    rgba(103, 247, 86, 0.05) 100%
                  )
                `,
                backdropFilter: "blur(20px)",
                border: "2px solid rgba(103, 247, 86, 0.2)",
                borderRadius: "24px",
                position: "relative",
                overflow: "hidden",
              }}
            >
              <Typography
                variant="h4"
                sx={{
                  fontWeight: 700,
                  mb: 2,
                  color: "primary.main",
                }}
              >
                Werden Sie Teil unserer zufriedenen Gäste
              </Typography>
              <Typography
                variant="body1"
                sx={{
                  color: "text.secondary",
                  display: "block",
                  fontSize: { xs: 16, md: 18 },
                  mb: 4,
                  maxWidth: "600px",
                  mx: "auto",
                  lineHeight: 1.6,
                }}
              >
                Buchen Sie jetzt Ihren unvergesslichen Abend bei Vibes Rooftop
                und erleben Sie selbst, warum unsere Gäste uns so lieben.
              </Typography>
              <a href="#form">
                <Button
                  variant="contained"
                  size="large"
                  sx={{
                    px: 4,
                    py: 1.5,
                    fontSize: 18,
                    fontWeight: 700,
                    borderRadius: "50px",
                    textTransform: "none",
                    boxShadow: "0 8px 24px rgba(103, 247, 86, 0.3)",
                    "&:hover": {
                      transform: "translateY(-2px)",
                      boxShadow: "0 12px 32px rgba(103, 247, 86, 0.4)",
                    },
                  }}
                >
                  Jetzt reservieren
                </Button>
              </a>
            </Paper>
          </Box>
        </Fade>
      </Container>
    </Box>
  );
};

export default ReviewWidget;
