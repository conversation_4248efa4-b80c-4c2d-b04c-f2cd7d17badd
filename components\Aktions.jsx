import pxToRem from "@/ThemeRegistry/pxToRem";
import { Box, Container, Grid, Typography } from "@mui/material";
import React from "react";
import { BsChatSquareQuote } from "react-icons/bs";

function Aktions() {
  return (
    <Box
      sx={{
        position: "relative",
        background: `
          radial-gradient(circle at 20% 80%, rgba(103, 247, 86, 0.1) 0%, transparent 50%),
          radial-gradient(circle at 80% 20%, rgba(74, 29, 31, 0.15) 0%, transparent 50%),
          url(https://images.ctfassets.net/87jhdyn6f199/3MG1zMUNfXupC1WeANfhjn/1bf78f115f2f87539da8c17d9f840ee1/sommeraktion.jpg),
          linear-gradient(135deg, #09090B 0%, #0f0f0f 25%, #1a1a1a 50%, #0f0f0f 75%, #09090B 100%)
        `,
        height: { xs: "350px", sm: "500px", xl: "600px" },
        width: "100%",
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
        backgroundPosition: "bottom",
        display: "flex",
        flexDirection: "column",
        justifyContent: "flex-start",
        paddingTop: { xs: 4, sm: 6, md: 8 },
        color: "white",
        overflow: "hidden",
      }}
    >
      {/* Overlay for text contrast */}
      <Box
        sx={{
          position: "absolute",
          inset: 0,
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.6) 0%, rgba(0,0,0,0.1) 100%)",
          zIndex: 1,
        }}
      />

      <Container sx={{ position: "relative", zIndex: 2 }}>
        <Grid container spacing={2} justifyContent="center">
          <Grid item xs={12} textAlign="center">
            <Typography
              variant="h1"
              component="h2"
              gutterBottom
              sx={{
                fontSize: `clamp(${pxToRem(26)}, 6vw, ${pxToRem(48)})`,
                fontWeight: 700,
                textShadow: "0 2px 8px rgba(0,0,0,0.7)",
              }}
            >
              “VIBES – FÜR ALLE, DIE MEHR WOLLEN”
            </Typography>
          </Grid>
          <Grid item xs={12} textAlign="center">
            <BsChatSquareQuote size={45} color="#67f756" />
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
}

export default Aktions;
