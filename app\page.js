import HeroComponent from "@/components/Hero";
import {BsWhatsapp} from "react-icons/bs";
import Container from "@mui/material/Container";
import Grid from "@mui/material/Grid";
import Typography from "@mui/material/Typography";
import Divider from "@mui/material/Divider";
import Box from "@mui/material/Box";
import Image from "next/image";
import Button from "@mui/material/Button";
import React from "react";
import Link from "next/link";
import ReserveForm from "@/components/forms/ReserveForm";
import Events from "@/components/Events";
import ReviewWidget from "@/components/ReviewWidget";
import Aktions from "@/components/Aktions";
import ImageGridSection from "@/components/ImageGridSection";
import GridSection from "@/components/GridSection";
// import YearBanner from "@/components/YearBanner";

export default async function Home() {
  return (
    <>
      <HeroComponent />
      <Events />
      <ReserveForm />


      {/* <Divider /> */}
      {/* https://images.ctfassets.net/87jhdyn6f199/LnCtjV6fF1YuDLErImOdy/2e733b1476865b1304ad23e0cdf9d1a8/winteraktion.jpg */}
      <Aktions />
      <Divider />
      <GridSection />
      {/* Modern review widget container */}
      <ReviewWidget />
      <ImageGridSection /> 
      <Divider />
      <Grid container spacing={0} my={5} alignItems={"center"}>
        <Grid item xs={12} md={8}>
          <Grid container>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/1SSfmxI2FCwLG0Ce3np27J/bb423073b0f0099c918540962f799684/hookah.jpg"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/56DVoyLC2c2CexEqKJH2Ky/bb902e800d824f0ebc96e2d065e609d4/photo_2023-11-06_12-25-23.jpg"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/7ooWbJ06KiPYPrVxrKfNNi/35e992f2c15769cc80c822a764ecb537/IMG_0007.JPG"
                  alt="test"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/5rsv75gR8HM4oDvAkVjCph/b6911ea7e25f716e792ddabf50fd7162/cooktail.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/4vehjxrR07huEAfiAAgV05/416c531c3f923598a7ecb4656baf6ff2/drink.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
            <Grid item xs={4} md={1.9}>
              <Box className="image-container" sx={{transition: "0.3s ease-in-out", "&:hover": {transform: "scale(0.9)"}, overflow: "hidden"}}>
                <Image
                  src="https://images.ctfassets.net/87jhdyn6f199/1WYNI0v3rkwMZvJPhfG4cm/42c7620097d4c702939417f594f84d19/vibes.jpg"
                  alt="vibes-rooftop"
                  fill
                  className="image"
                />
                <Box sx={{
                  transition: "0.3s ease-in-out",
                  position: "absolute",
                  top: 0,
                  left: 0,
                  height: "100%",
                  width: "100%",
                  backgroundColor: "transparent",
                  opacity: 0.30,
                  "&:hover": {
                    backgroundImage: "linear-gradient(180deg, #67f756 0%, #67f756 100%)",
                    transition: "0.3s ease-in-out",
                    top: 15,
                    left: 15,
                  }
                }} />
              </Box>
            </Grid>
          </Grid>
        </Grid>
        <Grid
          item
          xs={12}
          md={4}
          sx={{ "&:hover": { color: "#67f756" }, ml: {xs: 2, md: 0}, mt: {xs: 3, md: 0} }}
        >
          <a href="https://wa.me/41763652300">
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <BsWhatsapp fontSize={30} color="#67f756" />
              <Typography variant="body1" fontWeight={300}>Kontaktieren Sie uns über WhatsApp</Typography>
            </Box>
          </a>
        </Grid>
      </Grid>
      {/* <section>
        <YearBanner />
      </section> */}
    </>
  );
}
